from django.test import TestCase
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient
from rest_framework import status

User = get_user_model()


class AccountsTestCase(TestCase):
    """
    Test case for the accounts app.
    """

    def setUp(self):
        """
        Set up test data.
        """
        self.client = APIClient()
        self.register_url = reverse('register')
        self.login_url = reverse('login')
        self.profile_url = reverse('profile')

        # Test user data
        self.user_data = {
            'email': '<EMAIL>',
            'username': 'testuser',
            'password': 'TestPassword123!',
            'password2': 'TestPassword123!',
            'first_name': 'Test',
            'last_name': 'User'
        }

        # Create a test user for login tests
        self.test_user = User.objects.create_user(
            email='<EMAIL>',
            username='existinguser',
            password='ExistingPassword123!',
            first_name='Existing',
            last_name='User'
        )

    def test_user_registration(self):
        """
        Test user registration.
        """
        response = self.client.post(
            self.register_url,
            self.user_data,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertIn('user', response.data['data'])
        self.assertIn('tokens', response.data['data'])

        # Check that the user was created in the database
        self.assertTrue(
            User.objects.filter(email=self.user_data['email']).exists()
        )

    def test_user_login(self):
        """
        Test user login.
        """
        login_data = {
            'email': '<EMAIL>',
            'password': 'ExistingPassword123!'
        }

        response = self.client.post(
            self.login_url,
            login_data,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertIn('user', response.data['data'])
        self.assertIn('tokens', response.data['data'])

    def test_get_user_profile(self):
        """
        Test retrieving user profile.
        """
        # Login first to get token
        login_data = {
            'email': '<EMAIL>',
            'password': 'ExistingPassword123!'
        }

        login_response = self.client.post(
            self.login_url,
            login_data,
            format='json'
        )

        # Extract token
        token = login_response.data['data']['tokens']['access']

        # Set token in header
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {token}')

        # Get profile
        response = self.client.get(self.profile_url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['email'], '<EMAIL>')

    def test_invalid_login(self):
        """
        Test login with invalid credentials.
        """
        login_data = {
            'email': '<EMAIL>',
            'password': 'WrongPassword123!'
        }

        response = self.client.post(
            self.login_url,
            login_data,
            format='json'
        )

        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['message'], 'Invalid credentials.')
