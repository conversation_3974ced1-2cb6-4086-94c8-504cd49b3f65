#!/usr/bin/env python3
"""
Simple test script for bulk operations endpoints.
Run this script to test the bulk contact form and appointment form creation.

Usage:
    python test_bulk_operations.py

Requirements:
    - Django server running on localhost:8000
    - Valid JWT token for authentication
    - User with proper permissions
"""

import requests
import json
from datetime import datetime, date

# Configuration
BASE_URL = "http://localhost:8000/api"
JWT_TOKEN = "YOUR_JWT_TOKEN_HERE"  # Replace with actual token

# Headers for authenticated requests
HEADERS = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {JWT_TOKEN}"
}

def test_bulk_contact_forms():
    """Test bulk contact form creation."""
    print("Testing bulk contact form creation...")
    
    url = f"{BASE_URL}/website/contact-form/bulk/"
    
    # Test data
    data = {
        "contact_forms": [
            {
                "name": "Test User 1",
                "email": "<EMAIL>",
                "phone_number": "555-0001",
                "subject": "Test Subject 1",
                "message": "This is a test message for bulk operation 1."
            },
            {
                "name": "Test User 2",
                "email": "<EMAIL>",
                "phone_number": "555-0002",
                "subject": "Test Subject 2",
                "message": "This is a test message for bulk operation 2."
            },
            {
                "name": "Test User 3",
                "email": "<EMAIL>",
                "phone_number": "555-0003",
                "subject": "Test Subject 3",
                "message": "This is a test message for bulk operation 3."
            }
        ]
    }
    
    try:
        response = requests.post(url, headers=HEADERS, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 201:
            print("✅ Bulk contact form creation successful!")
        else:
            print("❌ Bulk contact form creation failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_bulk_appointment_forms():
    """Test bulk appointment form creation."""
    print("\nTesting bulk appointment form creation...")
    
    url = f"{BASE_URL}/website/appointment-form/bulk/"
    
    # Test data
    data = {
        "appointment_forms": [
            {
                "name": "Appointment Test User 1",
                "email": "<EMAIL>",
                "phone_number": "555-1001",
                "date_of_birth": "1990-01-15",
                "gender": "Male",
                "type_of_service": "Individual Therapy",
                "reason_for_seeking_therapy": "Anxiety and stress management",
                "insurance": "Blue Cross Blue Shield",
                "preferred_callback_time": "14:30:00",
                "how_did_you_hear_about_us": "Google search",
                "additional_comments": "Prefer morning appointments",
                "street_address": "123 Test Street",
                "city": "Test City",
                "state": "Test State",
                "zip_code": "12345",
                "date": "2024-02-01"
            },
            {
                "name": "Appointment Test User 2",
                "email": "<EMAIL>",
                "phone_number": "555-1002",
                "date_of_birth": "1985-05-20",
                "gender": "Female",
                "type_of_service": "Couples Therapy",
                "reason_for_seeking_therapy": "Relationship counseling",
                "insurance": "Aetna",
                "preferred_callback_time": "16:00:00",
                "how_did_you_hear_about_us": "Referral from friend",
                "additional_comments": "Evening appointments preferred",
                "street_address": "456 Test Avenue",
                "city": "Test City",
                "state": "Test State",
                "zip_code": "12346",
                "date": "2024-02-02"
            }
        ]
    }
    
    try:
        response = requests.post(url, headers=HEADERS, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 201:
            print("✅ Bulk appointment form creation successful!")
        else:
            print("❌ Bulk appointment form creation failed!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_validation_errors():
    """Test validation error handling."""
    print("\nTesting validation error handling...")
    
    url = f"{BASE_URL}/website/contact-form/bulk/"
    
    # Test data with invalid email
    data = {
        "contact_forms": [
            {
                "name": "Test User",
                "email": "invalid-email",  # Invalid email format
                "phone_number": "555-0001",
                "subject": "Test Subject",
                "message": "This should fail validation."
            }
        ]
    }
    
    try:
        response = requests.post(url, headers=HEADERS, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 400:
            print("✅ Validation error handling working correctly!")
        else:
            print("❌ Validation error handling not working as expected!")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run all tests."""
    print("🚀 Starting bulk operations tests...")
    print("=" * 50)
    
    if JWT_TOKEN == "YOUR_JWT_TOKEN_HERE":
        print("❌ Please update the JWT_TOKEN variable with a valid token!")
        return
    
    # Run tests
    test_bulk_contact_forms()
    test_bulk_appointment_forms()
    test_validation_errors()
    
    print("\n" + "=" * 50)
    print("🏁 Tests completed!")

if __name__ == "__main__":
    main()
