from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APIClient
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth.models import Group, Permission
from django.contrib.contenttypes.models import ContentType
from .models import ContactForm
from .repository import WebsiteRepository
from .services import WebsiteService

User = get_user_model()

class BaseTestCase(TestCase):
    def setUp(self):
        # Create a test contact form entry
        self.contact_form = ContactForm.objects.create(
            name='Test User',
            email='<EMAIL>',
            phone_number='1234567890',
            subject='Test Subject',
            message='Test Message'
        )

        # Data forcontact form
        self.contact_form_data = {
            'name': 'Test User',
            'email': '<EMAIL>',
            'phone_number': '9876543210',
            'subject': 'Test Subject',
            'message': 'Test Message'
        }

class ModelTests(BaseTestCase):
    def test_creation(self):
        """Test creating a ContactForm model instance"""
        contact_form = ContactForm.objects.create(
            name="Model Test User",
            email="<EMAIL>",
            phone_number="1122334455",
            subject="Model Test Subject",
            message="This is a test message for model testing."
        )

        self.assertEqual(contact_form.name, "Model Test User")
        self.assertEqual(contact_form.email, "<EMAIL>")
        self.assertEqual(contact_form.phone_number, "1122334455")
        self.assertEqual(contact_form.subject, "Model Test Subject")
        self.assertEqual(contact_form.message, "This is a test message for model testing.")
        self.assertIsNotNone(contact_form.timestamp)

    def test_str_method(self):
        """Test the __str__ method of ContactForm model"""
        contact_form = ContactForm.objects.create(
            name="String Test User",
            email="<EMAIL>",
            phone_number="5566778899",
            subject="String Test Subject",
            message="Testing the string representation."
        )

        self.assertEqual(str(contact_form), "String Test User")


class RepositoryTests(BaseTestCase):
    def test_create(self):
        """Test creating a contact form through the repository"""
        repo = WebsiteRepository()
        contact_data = {
            "name": "Repo Test User",
            "email": "<EMAIL>",
            "phone_number": "9988776655",
            "subject": "Repo Test Subject",
            "message": "Testing the repository create method."
        }

        response = repo.create_contact_form(**contact_data)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Submission created successfully.")
        self.assertEqual(response.data.name, "Repo Test User")
        self.assertEqual(response.data.email, "<EMAIL>")

    def test_get_all(self):
        """Test retrieving all contact forms through the repository"""
        # Create additional contact forms
        ContactForm.objects.create(
            name="Repo Get All Test 1",
            email="<EMAIL>",
            phone_number="1231231231",
            subject="Repo Get All Test 1",
            message="Testing get all contact forms 1."
        )

        ContactForm.objects.create(
            name="Repo Get All Test 2",
            email="<EMAIL>",
            phone_number="4564564564",
            subject="Repo Get All Test 2",
            message="Testing get all contact forms 2."
        )

        repo = WebsiteRepository()
        response = repo.get_all_contact_forms()

        self.assertTrue(response.success)
        self.assertGreaterEqual(len(response.data), 3)

    def test_get_by_id(self):
        """Test retrieving a contact form by ID through the repository"""
        repo = WebsiteRepository()
        response = repo.get_contact_form_by_id(self.contact_form.id)

        self.assertTrue(response.success)
        self.assertEqual(response.data.id, self.contact_form.id)
        self.assertEqual(response.data.name, "Test User")

    def test_get_nonexistent_contact_form(self):
        """Test retrieving a non-existent contact form through the repository"""
        repo = WebsiteRepository()
        response = repo.get_contact_form_by_id(856461)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Contact form entry not found.")

    def test_delete_contact_form(self):
        """Test deleting a contact form through the repository"""
        # Create a contact form to delete
        contact_form = ContactForm.objects.create(
            name="Delete Test User",
            email="<EMAIL>",
            phone_number="7897897890",
            subject="Delete Test Subject",
            message="Testing the repository delete method."
        )

        repo = WebsiteRepository()
        response = repo.delete_contact_form(contact_form.id)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Contact form entry deleted successfully.")

        # Verify it's deleted
        with self.assertRaises(ContactForm.DoesNotExist):
            ContactForm.objects.get(id=contact_form.id)

    def test_delete_nonexistent_contact_form(self):
        """Test deleting a non-existent contact form through the repository"""
        repo = WebsiteRepository()
        response = repo.delete_contact_form(856461)

        self.assertFalse(response.success)
        self.assertEqual(response.message, "Contact form entry not found.")


class ServiceTests(BaseTestCase):
    def test_create_contact_form(self):
        """Test creating a contact form through the service layer"""
        service = WebsiteService()
        contact_data = {
            "name": "Service Test User",
            "email": "<EMAIL>",
            "phone_number": "1212121212",
            "subject": "Service Test Subject",
            "message": "Testing the service create method."
        }

        response = service.create_contact_form(contact_data)

        self.assertTrue(response.success)
        self.assertEqual(response.message, "Submission created successfully.")
        self.assertEqual(response.status, 201)
        self.assertEqual(response.data.name, "Service Test User")
        self.assertEqual(response.data.email, "<EMAIL>")

    def test_get_all_contact_forms(self):
        """Test retrieving all contact forms through the service layer"""
        service = WebsiteService()
        response = service.get_all_contact_form_entries()

        self.assertTrue(response.success)
        self.assertEqual(response.status, 200)
        self.assertGreater(len(response.data), 0)

    def test_get_form_by_id(self):
        """Test retrieving a contact form by ID through the service layer"""
        service = WebsiteService()
        response = service.get_contact_form_by_id(self.contact_form.id)

        self.assertTrue(response.success)
        self.assertEqual(response.status, 200)
        self.assertEqual(response.data.id, self.contact_form.id)
        self.assertEqual(response.data.name, "Test User")

    def test_get_nonexistent_contact_form(self):
        """Test retrieving a non-existent contact form through the service layer"""
        service = WebsiteService()
        response = service.get_contact_form_by_id(856461)

        self.assertFalse(response.success)
        self.assertEqual(response.status, 404)
        self.assertEqual(response.message, "Contact form entry not found.")

    def test_delete_contact_form(self):
        """Test deleting a contact form through the service layer"""
        # Create a contact form to delete
        contact_form = ContactForm.objects.create(
            name="Service Delete Test",
            email="<EMAIL>",
            phone_number="3333333333",
            subject="Service Delete Test",
            message="Testing the service delete method."
        )

        service = WebsiteService()
        response = service.delete_contact_form(contact_form.id)

        self.assertTrue(response.success)
        self.assertEqual(response.status, 200)
        self.assertEqual(response.message, "Contact form entry deleted successfully.")

        # Verify it's deleted
        with self.assertRaises(ContactForm.DoesNotExist):
            ContactForm.objects.get(id=contact_form.id)

    def test_delete_nonexistent_contact_form(self):
        """Test deleting a non-existent contact form through the service layer"""
        service = WebsiteService()
        response = service.delete_contact_form(856461)

        self.assertFalse(response.success)
        self.assertEqual(response.status, 404)
        self.assertEqual(response.message, "Contact form entry not found.")


class APITests(BaseTestCase):
    def setUp(self):
        super().setUp()
        self.client = APIClient()
        self.create_contact_form_url = reverse('contact_form')
        self.contact_form_detail_url = reverse('contact_form_detail', kwargs={'contact_form_id': self.contact_form.id})

        # Create a test admin user
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            username='adminuser',
            password='securepassword',
            is_staff=True
        )

        # Create a test regular user
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            username='user',
            password='securepassword',
            is_staff=False
        )

        # Set up permissions for ContactForm model
        content_type = ContentType.objects.get_for_model(ContactForm)

        # Get or create permissions
        view_permission, _ = Permission.objects.get_or_create(
            codename='view_contactform',
            name='Can view contact form',
            content_type=content_type,
        )
        add_permission, _ = Permission.objects.get_or_create(
            codename='add_contactform',
            name='Can add contact form',
            content_type=content_type,
        )
        delete_permission, _ = Permission.objects.get_or_create(
            codename='delete_contactform',
            name='Can delete contact form',
            content_type=content_type,
        )

        # Create Admin group
        self.admin_group, _ = Group.objects.get_or_create(name='Admin')

        # Add permissions to Admin group
        self.admin_group.permissions.add(view_permission)
        self.admin_group.permissions.add(add_permission)
        self.admin_group.permissions.add(delete_permission)

        # Add admin user to Admin group
        self.admin_user.groups.add(self.admin_group)

    def authenticate(self, is_admin=True):
        """Helper method to authenticate the client

        Args:
            is_admin: Whether to authenticate as an admin user or a regular user
        """
        user = self.admin_user if is_admin else self.regular_user
        refresh = RefreshToken.for_user(user)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {refresh.access_token}')

    def test_create(self):
        """Test creating a contact form through the API"""
        response = self.client.post(
            self.create_contact_form_url,
            self.contact_form_data,
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Test User')
        self.assertEqual(response.data['data']['email'], '<EMAIL>')

    def test_admin_get_all(self):
        """Test that admin users can get all contact form entries"""
        # Authenticate as admin
        self.authenticate(is_admin=True)

        response = self.client.get(self.create_contact_form_url)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(len(response.data['data']), 1)
        self.assertEqual(response.data['data'][0]['name'], 'Test User')

    def test_regular_user_get_all(self):
        """Test that regular users cannot get all contact form entries"""
        # Authenticate as regular user
        self.authenticate(is_admin=False)

        response = self.client.get(self.create_contact_form_url)

        self.assertEqual(response.status_code, 403)

    def test_admin_get_by_id(self):
        """Test that admin users can get a specific contact form entry"""
        # Authenticate as admin
        self.authenticate(is_admin=True)

        response = self.client.get(self.contact_form_detail_url)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Test User')

    def test_regular_user_get_by_id(self):
        """Test that regular users cannot get a specific contact form entry"""
        # Authenticate as regular user
        self.authenticate(is_admin=False)

        response = self.client.get(self.contact_form_detail_url)

        self.assertEqual(response.status_code, 403)

    def test_admin_delete(self):
        """Test that admin users can delete a contact form entry"""
        # Create a new contact form to delete (since other tests might have deleted the original)
        new_contact_form = ContactForm.objects.create(
            name='Delete API Test',
            email='<EMAIL>',
            phone_number='5555555555',
            subject='Delete API Test',
            message='Testing API delete endpoint.'
        )
        delete_url = reverse('contact_form_detail', kwargs={'contact_form_id': new_contact_form.id})

        # Authenticate as admin
        self.authenticate(is_admin=True)

        response = self.client.delete(delete_url)

        self.assertEqual(response.status_code, 200)
        self.assertTrue(response.data['success'])
        self.assertEqual(response.data['message'], 'Contact form entry deleted successfully.')

    def test_regular_user_delete(self):
        """Test that regular users cannot delete a contact form entry"""
        new_contact_form = ContactForm.objects.create(
            name='Another Test User',
            email='<EMAIL>',
            phone_number='9876543210',
            subject='Another Test Subject',
            message='Another Test Message'
        )
        delete_url = reverse('contact_form_detail', kwargs={'contact_form_id': new_contact_form.id})

        # Authenticate as regular user
        self.authenticate(is_admin=False)

        response = self.client.delete(delete_url)

        self.assertEqual(response.status_code, 403)

    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access admin-only endpoints"""
        # Clear any existing authentication
        self.client.credentials()

        get_all_response = self.client.get(self.create_contact_form_url)
        get_by_id_response = self.client.get(self.contact_form_detail_url)
        delete_response = self.client.delete(self.contact_form_detail_url)

        self.assertEqual(get_all_response.status_code, 401)
        self.assertEqual(get_by_id_response.status_code, 403)
        self.assertEqual(delete_response.status_code, 403)

    def test_public_create(self):
        """Test that unauthenticated users can create contact form entries"""
        # Clear any existing authentication
        self.client.credentials()

        # new test data
        new_contact_form_data = {
            'name': 'Public User',
            'email': '<EMAIL>',
            'phone_number': '5555555555',
            'subject': 'Public Subject',
            'message': 'This is a message from an unauthenticated user.'
        }

        response = self.client.post(
            self.create_contact_form_url,
            new_contact_form_data,
            format='json'
        )

        self.assertEqual(response.status_code, 201)
        self.assertTrue(response.data['success'])
        self.assertIn('data', response.data)
        self.assertEqual(response.data['data']['name'], 'Public User')
        self.assertEqual(response.data['data']['email'], '<EMAIL>')

    def test_create_invalid_data(self):
        """Test creating a contact form with invalid data"""
        invalid_data = {
            "name": "",
            "email": "invalid-email",
            "phone_number": "123",
            "subject": "Test Subject",
            "message": "Test message"
        }

        response = self.client.post(
            self.create_contact_form_url,
            invalid_data,
            format='json'
        )

        self.assertEqual(response.status_code, 400)
        self.assertFalse(response.data['success'])
        self.assertIn('errors', response.data)

    def test_get_nonexistent_contact_form(self):
        """Test retrieving a non-existent contact form through the view"""
        self.authenticate(is_admin=True)

        # Use a non-existent ID
        nonexistent_url = reverse('contact_form_detail', kwargs={'contact_form_id': 856461})
        response = self.client.get(nonexistent_url)

        self.assertEqual(response.status_code, 404)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['message'], "Contact form entry not found.")

    def test_delete_nonexistent_contact_form(self):
        """Test deleting a non-existent contact form through the view"""
        self.authenticate(is_admin=True)

        # Use a non-existent ID
        nonexistent_url = reverse('contact_form_detail', kwargs={'contact_form_id': 856461})
        response = self.client.delete(nonexistent_url)

        self.assertEqual(response.status_code, 404)
        self.assertFalse(response.data['success'])
        self.assertEqual(response.data['message'], "Contact form entry not found.")
