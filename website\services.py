from core.data_classes import APIResponse
from .repository import WebsiteRepository


class WebsiteService:
    """
    Service for website-related business logic.
    """
    
    def __init__(self):
        self.repository = WebsiteRepository()
    
    def create_contact_form(self, contact_data) -> APIResponse:
        """
        Create a new contact form entry.
        """
        repo_response = self.repository.create_contact_form(**contact_data)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=400
            )
        
        contact_form = repo_response.data
        
        return APIResponse(
            success=True,
            message="Submission created successfully.",
            data=contact_form,
            status=201
        )
    
    def get_all_contact_form_entries(self) -> APIResponse:
        """
        Get all contact form entries.
        
        Returns:
            APIResponse: Response with success status, message, and list of contact forms
        """
        repo_response = self.repository.get_all_contact_forms()
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=400
            )
        
        contact_forms = repo_response.data
        
        return APIResponse(
            success=True,
            data=contact_forms,
            status=200
        )
    
    def get_contact_form_by_id(self, contact_form_id) -> APIResponse:
        """
        Get a contact form entry by its ID.
        """
        repo_response = self.repository.get_contact_form_by_id(contact_form_id)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=404 if "not found" in repo_response.message else 400
            )
        
        contact_form = repo_response.data
        
        return APIResponse(
            success=True,
            data=contact_form,
            status=200
        )
    
    def delete_contact_form(self, contact_form_id) -> APIResponse:
        """
        Delete a contact form entry.
        """
        repo_response = self.repository.delete_contact_form(contact_form_id)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=404 if "not found" in repo_response.message else 400
            )
        
        return APIResponse(
            success=True,
            message="Contact form entry deleted successfully.",
            status=200
        )
    
    # Appointment Form Services
    def create_appointment_form(self, appointment_data) -> APIResponse:
        """
        Create a new appointment form entry.
        """
        repo_response = self.repository.create_appointment_form(**appointment_data)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=400
            )
        
        appointment_form = repo_response.data
        
        return APIResponse(
            success=True,
            message="Appointment created successfully.",
            data=appointment_form,
            status=201
        )
    
    def get_all_appointment_form_entries(self) -> APIResponse:
        """
        Get all appointment form entries.
        
        Returns:
            APIResponse: Response with success status, message, and list of appointment forms
        """
        repo_response = self.repository.get_all_appointment_forms()
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=400
            )
        
        appointment_forms = repo_response.data
        
        return APIResponse(
            success=True,
            data=appointment_forms,
            status=200
        )
    
    def get_appointment_form_by_id(self, appointment_form_id) -> APIResponse:
        """
        Get an appointment form entry by its ID.
        """
        repo_response = self.repository.get_appointment_form_by_id(appointment_form_id)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=404 if "not found" in repo_response.message else 400
            )
        
        appointment_form = repo_response.data
        
        return APIResponse(
            success=True,
            data=appointment_form,
            status=200
        )
    
    def delete_appointment_form(self, appointment_form_id) -> APIResponse:
        """
        Delete an appointment form entry.
        """
        repo_response = self.repository.delete_appointment_form(appointment_form_id)
        
        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                status=404 if "not found" in repo_response.message else 400
            )
        
        return APIResponse(
            success=True,
            message="Appointment form entry deleted successfully.",
            status=200
        )

    # Bulk Operations
    def bulk_create_contact_forms(self, contact_forms_data) -> APIResponse:
        """
        Create multiple contact form entries.
        """
        if not contact_forms_data or not isinstance(contact_forms_data, list):
            return APIResponse(
                success=False,
                message="Invalid data format. Expected a list of contact form objects.",
                status=400
            )

        if len(contact_forms_data) == 0:
            return APIResponse(
                success=False,
                message="No contact forms provided.",
                status=400
            )

        # Limit bulk operations to prevent abuse
        if len(contact_forms_data) > 100:
            return APIResponse(
                success=False,
                message="Bulk operation limited to 100 items per request.",
                status=400
            )

        repo_response = self.repository.bulk_create_contact_forms(contact_forms_data)

        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                data=repo_response.data,
                status=400
            )

        contact_forms = repo_response.data

        return APIResponse(
            success=True,
            message=repo_response.message,
            data=contact_forms,
            status=201
        )

    def bulk_create_appointment_forms(self, appointment_forms_data) -> APIResponse:
        """
        Create multiple appointment form entries.
        """
        if not appointment_forms_data or not isinstance(appointment_forms_data, list):
            return APIResponse(
                success=False,
                message="Invalid data format. Expected a list of appointment form objects.",
                status=400
            )

        if len(appointment_forms_data) == 0:
            return APIResponse(
                success=False,
                message="No appointment forms provided.",
                status=400
            )

        # Limit bulk operations to prevent abuse
        if len(appointment_forms_data) > 100:
            return APIResponse(
                success=False,
                message="Bulk operation limited to 100 items per request.",
                status=400
            )

        repo_response = self.repository.bulk_create_appointment_forms(appointment_forms_data)

        if not repo_response.success:
            return APIResponse(
                success=False,
                message=repo_response.message,
                data=repo_response.data,
                status=400
            )

        appointment_forms = repo_response.data

        return APIResponse(
            success=True,
            message=repo_response.message,
            data=appointment_forms,
            status=201
        )