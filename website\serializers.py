from rest_framework import serializers
from .models import ContactForm, AppointmentForm

class ContactFormSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactForm
        fields = '__all__'

    def validate_name(self, value):
        if not value.strip():
            raise serializers.ValidationError("Name cannot be empty.")
        return value
    
class AppointmentFormSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppointmentForm
        fields = '__all__'
