from rest_framework import serializers
from .models import ContactForm, AppointmentForm

class ContactFormSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContactForm
        fields = '__all__'

    def validate_name(self, value):
        if not value.strip():
            raise serializers.ValidationError("Name cannot be empty.")
        return value
    
class AppointmentFormSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppointmentForm
        fields = '__all__'


class BulkContactFormSerializer(serializers.Serializer):
    """
    Serializer for bulk contact form creation.
    Accepts a list of contact form data.
    """
    contact_forms = ContactFormSerializer(many=True)

    def validate_contact_forms(self, value):
        if not value:
            raise serializers.ValidationError("At least one contact form is required.")

        if len(value) > 100:
            raise serializers.ValidationError("Maximum 100 contact forms allowed per bulk operation.")

        return value


class BulkAppointmentFormSerializer(serializers.Serializer):
    """
    Serializer for bulk appointment form creation.
    Accepts a list of appointment form data.
    """
    appointment_forms = AppointmentFormSerializer(many=True)

    def validate_appointment_forms(self, value):
        if not value:
            raise serializers.ValidationError("At least one appointment form is required.")

        if len(value) > 100:
            raise serializers.ValidationError("Maximum 100 appointment forms allowed per bulk operation.")

        return value
