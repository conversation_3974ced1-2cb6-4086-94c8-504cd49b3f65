from django.db import models

# Create your models here.
class ContactForm(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone_number = models.CharField(max_length=20)
    subject = models.CharField(max_length=100)
    message = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
    
class AppointmentForm(models.Model):
    name = models.CharField(max_length=100)
    email = models.EmailField()
    phone_number = models.CharField(max_length=20)
    date_of_birth = models.DateField()
    gender = models.Char<PERSON>ield(max_length=10)
    type_of_service = models.CharField(max_length=100)
    reason_for_seeking_therapy = models.TextField()
    insurance = models.CharField(max_length=100)
    preferred_callback_time = models.TimeField()
    how_did_you_hear_about_us = models.TextField()
    additional_comments = models.TextField()
    street_address = models.Char<PERSON>ield(max_length=100)
    city = models.CharField(max_length=100)
    state = models.Char<PERSON>ield(max_length=100)
    zip_code = models.Char<PERSON>ield(max_length=20)
    date = models.DateField()
    timestamp = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.name
