from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.response import Response
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework_simplejwt.tokens import RefreshToken

from .serializers import UserRegistrationSerializer, UserSerializer, CustomTokenObtainPairSerializer
from .services import AccountService


class CustomTokenObtainPairView(TokenObtainPairView):
    """
    Custom token view that uses our enhanced token serializer.
    """
    serializer_class = CustomTokenObtainPairSerializer


@api_view(['POST'])
@permission_classes([AllowAny])
def register_user(request):
    """
    Register a new user.
    """
    serializer = UserRegistrationSerializer(data=request.data)

    if serializer.is_valid():
        # Use the service to register the user
        service_response = AccountService().register_user(serializer.validated_data)

        if service_response.success:
            return Response(
                {
                    'success': service_response.success,
                    'message': service_response.message,
                    'data': service_response.data
                },
                status=service_response.status
            )
        else:
            return Response(
                {
                    'success': service_response.success,
                    'message': service_response.message,
                    'errors': service_response.errors
                },
                status=service_response.status
            )

    return Response(
        {
            'success': False,
            'message': 'Invalid data provided.',
            'errors': serializer.errors
        },
        status=status.HTTP_400_BAD_REQUEST
    )


@api_view(['POST'])
@permission_classes([AllowAny])
def login_user(request):
    """
    Authenticate a user and return tokens.
    """
    email = request.data.get('email')
    password = request.data.get('password')

    if not email or not password:
        return Response(
            {
                'success': False,
                'message': 'Please provide both email and password.'
            },
            status=status.HTTP_400_BAD_REQUEST
        )

    service_response = AccountService().login_user(email, password)

    return Response(
        {
            'success': service_response.success,
            'message': service_response.message,
            'data': service_response.data if service_response.success else None
        },
        status=service_response.status
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_user(request):
    """
    Logout a user by blacklisting both refresh and access tokens.
    """
    try:
        refresh_token = request.data.get('refresh')
        if refresh_token:
            # Get the token object
            token = RefreshToken(refresh_token)

            # Blacklist the refresh token
            token.blacklist()

            # Unfortunately, SimpleJWT doesn't provide a direct way to blacklist access tokens
            # Let's update the SIMPLE_JWT settings to use shorter access token lifetimes instead

            return Response(
                {
                    'success': True,
                    'message': 'Logout successful. Your refresh token has been blacklisted. Access tokens will expire in 15 minutes.'
                },
                status=status.HTTP_200_OK
            )
        else:
            return Response(
                {
                    'success': False,
                    'message': 'Refresh token is required.'
                },
                status=status.HTTP_400_BAD_REQUEST
            )
    except Exception as e:
        return Response(
            {
                'success': False,
                'message': f'Logout failed: {str(e)}'
            },
            status=status.HTTP_400_BAD_REQUEST
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_profile(request):
    """
    Get the authenticated user's profile.
    """
    service_response = AccountService().get_user_profile(request.user.id)

    return Response(
        {
            'success': service_response.success,
            'message': service_response.message if not service_response.success else 'Profile retrieved successfully.',
            'data': service_response.data
        },
        status=service_response.status
    )


@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def update_user_profile(request):
    """
    Update the authenticated user's profile.
    """
    serializer = UserSerializer(request.user, data=request.data, partial=True)

    if serializer.is_valid():
        service_response = AccountService().update_user_profile(
            request.user.id,
            serializer.validated_data
        )

        return Response(
            {
                'success': service_response.success,
                'message': service_response.message,
                'data': service_response.data if service_response.success else None
            },
            status=service_response.status
        )

    return Response(
        {
            'success': False,
            'message': 'Invalid data provided.',
            'errors': serializer.errors
        },
        status=status.HTTP_400_BAD_REQUEST
    )
