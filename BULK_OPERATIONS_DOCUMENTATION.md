# Bulk Operations API Documentation

This document describes the bulk operation endpoints for creating multiple contact forms and appointment forms in a single API call.

## Overview

The bulk operations allow you to create multiple forms at once, which is useful for:
- Data migration
- Batch imports
- Administrative operations
- Testing with multiple records

## Authentication & Permissions

Both bulk endpoints require:
- **Authentication**: User must be logged in
- **Permissions**: User must have form creation permissions (admin users)

## Endpoints

### 1. Bulk Contact Form Creation

**Endpoint:** `POST /api/website/contact-form/bulk/`

**Request Format:**
```json
{
  "contact_forms": [
    {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone_number": "************",
      "subject": "General Inquiry",
      "message": "I would like to know more about your services."
    },
    {
      "name": "<PERSON>",
      "email": "<EMAIL>",
      "phone_number": "************",
      "subject": "Appointment Request",
      "message": "I need to schedule an appointment."
    }
  ]
}
```

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Successfully created 2 contact form entries.",
  "created_count": 2,
  "data": [
    {
      "id": 1,
      "name": "<PERSON>e",
      "email": "<EMAIL>",
      "phone_number": "************",
      "subject": "General Inquiry",
      "message": "I would like to know more about your services.",
      "timestamp": "2024-01-20T10:30:00Z"
    },
    {
      "id": 2,
      "name": "Jane Smith",
      "email": "<EMAIL>",
      "phone_number": "************",
      "subject": "Appointment Request",
      "message": "I need to schedule an appointment.",
      "timestamp": "2024-01-20T10:30:01Z"
    }
  ]
}
```

### 2. Bulk Appointment Form Creation

**Endpoint:** `POST /api/website/appointment-form/bulk/`

**Request Format:**
```json
{
  "appointment_forms": [
    {
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone_number": "************",
      "date_of_birth": "1990-05-15",
      "gender": "Male",
      "type_of_service": "Individual Therapy",
      "reason_for_seeking_therapy": "Anxiety management",
      "insurance": "Blue Cross Blue Shield",
      "preferred_callback_time": "14:30:00",
      "how_did_you_hear_about_us": "Google search",
      "additional_comments": "Prefer evening appointments",
      "street_address": "123 Main Street",
      "city": "Springfield",
      "state": "Illinois",
      "zip_code": "62701",
      "date": "2024-01-25"
    }
  ]
}
```

**Success Response (201 Created):**
```json
{
  "success": true,
  "message": "Successfully created 1 appointment form entries.",
  "created_count": 1,
  "data": [
    {
      "id": 1,
      "name": "John Doe",
      "email": "<EMAIL>",
      "phone_number": "************",
      "date_of_birth": "1990-05-15",
      "gender": "Male",
      "type_of_service": "Individual Therapy",
      "reason_for_seeking_therapy": "Anxiety management",
      "insurance": "Blue Cross Blue Shield",
      "preferred_callback_time": "14:30:00",
      "how_did_you_hear_about_us": "Google search",
      "additional_comments": "Prefer evening appointments",
      "street_address": "123 Main Street",
      "city": "Springfield",
      "state": "Illinois",
      "zip_code": "62701",
      "date": "2024-01-25",
      "timestamp": "2024-01-20T10:30:00Z"
    }
  ]
}
```

## Error Responses

### Validation Errors (400 Bad Request)
```json
{
  "success": false,
  "message": "Invalid data provided.",
  "errors": {
    "contact_forms": [
      {
        "email": ["Enter a valid email address."]
      }
    ]
  }
}
```

### Authentication Required (401 Unauthorized)
```json
{
  "success": false,
  "message": "Authentication required."
}
```

### Permission Denied (403 Forbidden)
```json
{
  "success": false,
  "message": "You do not have permission to perform this action."
}
```

### Bulk Operation Failed (400 Bad Request)
```json
{
  "success": false,
  "message": "Bulk contact form creation failed: Some contact forms failed validation",
  "data": {
    "created_count": 0,
    "failed_forms": [
      {
        "index": 0,
        "data": {...},
        "error": "UNIQUE constraint failed: website_contactform.email"
      }
    ]
  }
}
```

## Limitations

- **Maximum Items**: 100 items per bulk operation
- **Transaction Safety**: All items are created in a single database transaction
- **All-or-Nothing**: If any item fails validation, the entire operation is rolled back
- **Authentication Required**: Only authenticated users with proper permissions can use bulk operations

## Usage Examples

### Using cURL

```bash
# Bulk create contact forms
curl -X POST http://localhost:8000/api/website/contact-form/bulk/ \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "contact_forms": [
      {
        "name": "Test User",
        "email": "<EMAIL>",
        "phone_number": "555-0123",
        "subject": "Test Subject",
        "message": "Test message"
      }
    ]
  }'
```

### Using JavaScript/Fetch

```javascript
const response = await fetch('/api/website/contact-form/bulk/', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    contact_forms: [
      {
        name: "Test User",
        email: "<EMAIL>",
        phone_number: "555-0123",
        subject: "Test Subject",
        message: "Test message"
      }
    ]
  })
});

const result = await response.json();
console.log(result);
```

## Best Practices

1. **Validate Data**: Ensure all required fields are present before sending
2. **Handle Errors**: Always check the response for validation errors
3. **Batch Size**: Keep batch sizes reasonable (recommended: 10-50 items)
4. **Error Recovery**: Implement retry logic for failed operations
5. **Logging**: Log bulk operations for audit purposes
