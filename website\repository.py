from core.data_classes import RepositoryResponse
from .models import ContactForm, AppointmentForm
from .logging import LoggingService


logging_service = LoggingService()


class WebsiteRepository:
    """
    Repository for website-related database operations.
    """
    
    def create_contact_form(self, **contact_data) -> RepositoryResponse:
        """
        Create a new contact form entry in the database.
        """
        try:
            contact_form = ContactForm.objects.create(**contact_data)
            return RepositoryResponse(
                success=True,
                message="Submission created successfully.",
                data=contact_form
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Something went wrong: {str(e)}"
            )
        
    def get_all_contact_forms(self) -> RepositoryResponse:
        """
        Get all contact form entries from the database.
        """
        try:
            contact_forms = ContactForm.objects.all()
            return RepositoryResponse(
                success=True,
                data=contact_forms
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Failed to retrieve contact form entries: {str(e)}"
            )
        
    def get_contact_form_by_id(self, contact_form_id) -> RepositoryResponse:
        """
        Get a contact form entry by its ID.
        """
        try:
            contact_form = ContactForm.objects.get(id=contact_form_id)
            return RepositoryResponse(
                success=True,
                data=contact_form
            )
        except ContactForm.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Contact form entry not found."
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Failed to retrieve contact form entry: {str(e)}"
            )
        
    def delete_contact_form(self, contact_form_id) -> RepositoryResponse:
        """
        Delete a contact form entry from the database.
        """
        try:
            contact_form = ContactForm.objects.get(id=contact_form_id)
            contact_form.delete()
            return RepositoryResponse(
                success=True,
                message="Contact form entry deleted successfully."
            )
        except ContactForm.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Contact form entry not found."
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Failed to delete contact form entry: {str(e)}"
            )
    
    # Appointment Form Methods
    def create_appointment_form(self, **appointment_data) -> RepositoryResponse:
        """
        Create a new appointment form entry in the database.
        """
        try:
            appointment_form = AppointmentForm.objects.create(**appointment_data)
            return RepositoryResponse(
                success=True,
                message="Appointment created successfully.",
                data=appointment_form
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Something went wrong: {str(e)}"
            )

    def get_all_appointment_forms(self) -> RepositoryResponse:
        """
        Get all appointment form entries from the database.
        """
        try:
            appointment_forms = AppointmentForm.objects.all()
            return RepositoryResponse(
                success=True,
                data=appointment_forms
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Failed to retrieve appointment form entries: {str(e)}"
            )

    def get_appointment_form_by_id(self, appointment_form_id) -> RepositoryResponse:
        """
        Get an appointment form entry by its ID.
        """
        try:
            appointment_form = AppointmentForm.objects.get(id=appointment_form_id)
            return RepositoryResponse(
                success=True,
                data=appointment_form
            )
        except AppointmentForm.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Appointment form entry not found."
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Failed to retrieve appointment form entry: {str(e)}"
            )
        
    def delete_appointment_form(self, appointment_form_id) -> RepositoryResponse:
        """
        Delete an appointment form entry from the database.
        """
        try:
            appointment_form = AppointmentForm.objects.get(id=appointment_form_id)
            appointment_form.delete()
            return RepositoryResponse(
                success=True,
                message="Appointment form entry deleted successfully."
            )
        except AppointmentForm.DoesNotExist:
            return RepositoryResponse(
                success=False,
                message="Appointment form entry not found."
            )
        except Exception as e:
            logging_service.log_error(e)
            return RepositoryResponse(
                success=False,
                message=f"Failed to delete appointment form entry: {str(e)}"
            )
         




        
